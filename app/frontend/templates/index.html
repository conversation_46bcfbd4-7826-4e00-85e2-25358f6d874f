<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试计划生成器</title>
    <link rel="stylesheet" href="/ui/static/css/style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🧪 测试计划生成器</h1>
            <p class="subtitle">将自然语言转换为结构化移动应用测试计划</p>
            <div class="model-info" id="model-info">
                <span class="model-badge">🤖 加载中...</span>
            </div>
        </header>

        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="generate">生成计划</button>
            <button class="tab-btn" data-tab="history">历史记录</button>
            <button class="tab-btn" data-tab="prompt">Prompt说明</button>
        </nav>

        <!-- 生成计划页面 -->
        <div class="tab-content active" id="generate-tab">
            <div class="input-section">
                <form id="generate-form">
                    <div class="form-group">
                        <label for="instruction">测试指令</label>
                        <textarea 
                            id="instruction" 
                            name="instruction"
                            placeholder="请输入自然语言测试指令，例如：打开美团外卖APP，搜索火锅，选择第一家店，加入购物车后下单"
                            rows="4"
                            required
                        ></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="platform">目标平台</label>
                        <select id="platform" name="platform">
                            <option value="">自动选择</option>
                            <option value="android">Android</option>
                            <option value="ios">iOS</option>
                        </select>
                    </div>
                    
                    <button type="submit" id="generate-btn" class="btn btn-primary">
                        <span class="btn-text">生成测试计划</span>
                        <span class="btn-loader" style="display: none;">生成中...</span>
                    </button>
                </form>
            </div>

            <!-- 系统状态 -->
            <div class="system-status" id="system-status" style="display: none;">
                <div class="status-indicator">
                    <span class="status-icon">⚡</span>
                    <span class="status-text">系统负载正常</span>
                </div>
            </div>

            <!-- 排队信息 -->
            <div class="queue-info" id="queue-info" style="display: none;">
                <div class="queue-card">
                    <h3>🚦 系统繁忙</h3>
                    <p>当前服务器负载较高，您的请求已加入队列。</p>
                    <div class="queue-details">
                        <span class="queue-position">队列位置: <strong id="queue-position">-</strong></span>
                        <span class="estimated-time">预计等待: <strong id="estimated-time">-</strong></span>
                    </div>
                </div>
            </div>

            <!-- 生成进度 -->
            <div class="generation-progress" id="generation-progress" style="display: none;">
                <div class="progress-header">
                    <h3>🔄 正在生成测试计划</h3>
                    <div class="progress-stats">
                        <span id="progress-chars">0 字符</span>
                        <span id="progress-time">0s</span>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-content">
                    <div class="content-preview" id="content-preview">
                        <h4>生成内容预览:</h4>
                        <div class="preview-text" id="preview-text"></div>
                    </div>
                </div>
            </div>

            <!-- 生成结果 -->
            <div class="result-section" id="result-section" style="display: none;">
                <div class="result-header">
                    <h3>✅ 生成完成</h3>
                    <div class="result-meta">
                        <span id="result-time"></span>
                        <span id="result-length"></span>
                    </div>
                </div>
                <div class="raw-output-preview" id="raw-output-preview">
                    <h4>原始输出:</h4>
                    <div class="raw-output-text" id="raw-output-text"></div>
                </div>
                <div class="result-actions">
                    <button class="btn btn-primary" id="parse-json-btn">🔍 解析JSON计划</button>
                    <button class="btn btn-secondary" id="copy-raw-output">复制原始输出</button>
                    <button class="btn btn-secondary" id="retry-btn">重试</button>
                </div>
                
                <!-- JSON解析结果区域 -->
                <div class="json-parse-result" id="json-parse-result" style="display: none;">
                    <div class="parse-status" id="parse-status"></div>
                    <div class="parsed-plan" id="parsed-plan" style="display: none;">
                        <h4>解析后的测试计划:</h4>
                        <div class="plan-summary" id="plan-summary"></div>
                        <div class="plan-actions">
                            <button class="btn btn-secondary" id="copy-parsed-json">复制JSON</button>
                            <button class="btn btn-secondary" id="view-plan-details">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 错误信息 -->
            <div class="error-section" id="error-section" style="display: none;">
                <div class="error-card">
                    <h3>❌ 生成失败</h3>
                    <p id="error-message"></p>
                    <button class="btn btn-secondary" id="retry-btn">重试</button>
                </div>
            </div>
        </div>

        <!-- 历史记录页面 -->
        <div class="tab-content" id="history-tab">
            <div class="history-controls">
                <div class="search-section">
                    <input type="text" id="history-search" placeholder="搜索历史记录...">
                    <select id="history-platform">
                        <option value="">所有平台</option>
                        <option value="android">Android</option>
                        <option value="ios">iOS</option>
                    </select>
                    <select id="history-status">
                        <option value="">所有状态</option>
                        <option value="success">成功</option>
                        <option value="failed">失败</option>
                        <option value="error">错误</option>
                    </select>
                    <button class="btn btn-secondary" id="search-history">搜索</button>
                </div>
            </div>

            <div class="history-list" id="history-list">
                <div class="loading" id="history-loading">
                    <span>📋 正在加载历史记录...</span>
                </div>
            </div>
        </div>

        <!-- Prompt说明页面 -->
        <div class="tab-content" id="prompt-tab">
            <div class="prompt-section">
                <div class="prompt-header">
                    <h3>📋 Prompt说明文档</h3>
                    <p class="prompt-subtitle">系统使用的详细prompt要求和规则说明</p>
                </div>
                
                <div class="prompt-content" id="prompt-content">
                    <div class="loading">
                        <span>📄 正在加载prompt说明...</span>
                    </div>
                </div>
                
                <div class="prompt-actions">
                    <button class="btn btn-secondary" id="refresh-prompt">刷新内容</button>
                    <button class="btn btn-secondary" id="copy-prompt">复制全部</button>
                </div>
            </div>
        </div>

        <!-- 原始响应模态框 -->
        <div class="modal" id="raw-response-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🔍 原始响应</h3>
                    <button class="modal-close" id="raw-modal-close">×</button>
                </div>
                <div class="modal-body">
                    <div class="raw-response-viewer" id="raw-response-viewer"></div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="copy-raw-response">复制原始响应</button>
                    <button class="btn btn-secondary" id="back-to-json">返回JSON视图</button>
                </div>
            </div>
        </div>

        <!-- JSON详情模态框 -->
        <div class="modal" id="json-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>📋 测试计划详情</h3>
                    <button class="modal-close" id="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <div class="json-viewer" id="json-viewer"></div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="copy-json">复制JSON</button>
                    <button class="btn btn-secondary" id="download-json">下载JSON</button>
                    <button class="btn btn-secondary" id="view-raw-response" style="display: none;">查看原始响应</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/ui/static/js/app.js"></script>
</body>
</html>