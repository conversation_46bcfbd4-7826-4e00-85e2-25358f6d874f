class TestPlanGenerator {
    constructor() {
        this.currentGeneration = null;
        this.systemLoad = 0;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initTabs();
        this.checkSystemStatus();
        this.loadModelInfo();
        this.loadHistory();
        
        // 定期检查系统状态
        setInterval(() => this.checkSystemStatus(), 10000);
    }

    bindEvents() {
        // 表单提交
        document.getElementById('generate-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.generatePlan();
        });

        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 历史记录搜索
        document.getElementById('search-history').addEventListener('click', () => {
            this.searchHistory();
        });

        // 模态框控制
        document.getElementById('modal-close').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('json-modal').addEventListener('click', (e) => {
            if (e.target.id === 'json-modal') {
                this.closeModal();
            }
        });

        // 复制和下载按钮
        document.getElementById('copy-json').addEventListener('click', () => {
            this.copyJSON();
        });

        document.getElementById('download-json').addEventListener('click', () => {
            this.downloadJSON();
        });

        // 结果操作按钮
        document.getElementById('parse-json-btn').addEventListener('click', () => {
            this.parseJSON();
        });

        document.getElementById('copy-raw-output').addEventListener('click', () => {
            this.copyRawOutput();
        });

        document.getElementById('copy-parsed-json').addEventListener('click', () => {
            this.copyParsedJSON();
        });

        document.getElementById('view-plan-details').addEventListener('click', () => {
            this.viewPlanDetails();
        });

        document.getElementById('retry-btn').addEventListener('click', () => {
            this.retryGeneration();
        });

        // Prompt页面操作按钮
        document.getElementById('refresh-prompt').addEventListener('click', () => {
            this.loadPromptContent();
        });

        document.getElementById('copy-prompt').addEventListener('click', () => {
            this.copyPromptContent();
        });

        // 原始响应模态框操作
        document.getElementById('view-raw-response').addEventListener('click', () => {
            this.showRawResponseModal();
        });

        document.getElementById('raw-modal-close').addEventListener('click', () => {
            this.closeRawResponseModal();
        });

        document.getElementById('copy-raw-response').addEventListener('click', () => {
            this.copyRawResponse();
        });

        document.getElementById('back-to-json').addEventListener('click', () => {
            this.backToJsonView();
        });

        // 原始响应模态框外部点击关闭
        document.getElementById('raw-response-modal').addEventListener('click', (e) => {
            if (e.target.id === 'raw-response-modal') {
                this.closeRawResponseModal();
            }
        });
    }

    initTabs() {
        this.switchTab('generate');
    }

    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 显示对应内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        // 如果切换到历史记录，刷新数据
        if (tabName === 'history') {
            this.loadHistory();
        }
        
        // 如果切换到prompt页面，加载prompt内容
        if (tabName === 'prompt') {
            this.loadPromptContent();
        }
    }

    async checkSystemStatus() {
        try {
            const response = await fetch('/health');
            const data = await response.json();
            
            // 从健康检查接口获取真实系统负载
            this.systemLoad = data.system?.load_percent || 0;
            
            const statusElement = document.getElementById('system-status');
            const statusText = statusElement.querySelector('.status-text');
            const statusIcon = statusElement.querySelector('.status-icon');
            
            if (this.systemLoad > 90) {
                statusElement.style.display = 'block';
                statusElement.className = 'system-status high-load';
                statusElement.style.background = '#f8d7da';
                statusElement.style.borderColor = '#f5c6cb';
                statusIcon.textContent = '⚠️';
                statusText.textContent = `系统负载较高 (${this.systemLoad.toFixed(1)}%)`;
            } else if (this.systemLoad > 70) {
                statusElement.style.display = 'block';
                statusElement.className = 'system-status medium-load';
                statusElement.style.background = '#fff3cd';
                statusElement.style.borderColor = '#ffeaa7';
                statusIcon.textContent = '⚡';
                statusText.textContent = `系统负载中等 (${this.systemLoad.toFixed(1)}%)`;
            } else {
                statusElement.style.display = 'block';
                statusElement.className = 'system-status normal-load';
                statusElement.style.background = '#d4edda';
                statusElement.style.borderColor = '#c3e6cb';
                statusIcon.textContent = '✅';
                statusText.textContent = `系统负载正常 (${this.systemLoad.toFixed(1)}%)`;
            }
        } catch (error) {
            console.error('检查系统状态失败:', error);
            // 降级处理
            this.systemLoad = 0;
        }
    }

    async loadModelInfo() {
        try {
            const response = await fetch('/test-plan/model-info');
            const modelData = await response.json();
            
            const modelBadge = document.querySelector('.model-badge');
            if (modelData.error) {
                modelBadge.textContent = `🤖 ${modelData.current_model}`;
                modelBadge.title = `错误: ${modelData.error}`;
            } else {
                const modelName = modelData.current_model;
                const modelCount = modelData.available_models_count || 0;
                const sizeInfo = modelData.model_details?.size ? 
                    ` (${(modelData.model_details.size / 1024 / 1024 / 1024).toFixed(1)}GB)` : '';
                
                modelBadge.textContent = `🤖 ${modelName}${sizeInfo}`;
                modelBadge.title = `当前模型: ${modelName}\n可用模型: ${modelCount}个\n点击查看详情`;
                
                // 添加点击事件显示详细信息
                modelBadge.onclick = () => this.showModelDetails(modelData);
            }
            
        } catch (error) {
            console.error('加载模型信息失败:', error);
            const modelBadge = document.querySelector('.model-badge');
            modelBadge.textContent = '🤖 加载失败';
            modelBadge.title = '无法获取模型信息';
        }
    }

    showModelDetails(modelData) {
        const details = modelData.model_details || {};
        const detailsText = `
模型名称: ${modelData.current_model}
可用模型数量: ${modelData.available_models_count || 0}个
模型大小: ${details.size ? (details.size / 1024 / 1024 / 1024).toFixed(2) + 'GB' : '未知'}
修改时间: ${details.modified_at || '未知'}
平台状态文件: ${modelData.platform_state_file || '未知'}
        `.trim();
        
        alert(detailsText);
    }

    async generatePlan() {
        const form = document.getElementById('generate-form');
        const formData = new FormData(form);
        
        const instruction = formData.get('instruction').trim();
        if (!instruction) {
            this.showError('请输入测试指令');
            return;
        }

        // 检查系统负载
        if (this.systemLoad > 90) {
            this.showQueueInfo();
            // 仍然继续生成，但在后台执行
        }

        this.resetUI();
        this.setGenerating(true);

        const requestData = {
            instruction: instruction,
            platform: formData.get('platform') || null,
            stream: true
        };

        try {
            const response = await fetch('/test-plan/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 处理流式响应
            await this.handleStreamResponse(response);

        } catch (error) {
            console.error('生成失败:', error);
            this.showError(`生成失败: ${error.message}`);
            this.setGenerating(false);
        }
    }

    async handleStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        let buffer = '';
        let startTime = Date.now();
        let finalResult = null;

        this.showProgress();

        try {
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                
                // 按行处理SSE数据
                const lines = buffer.split('\n');
                buffer = lines.pop(); // 保留不完整的行
                
                for (const line of lines) {
                    if (line.trim().startsWith('data: ')) {
                        try {
                            const jsonData = JSON.parse(line.slice(6));
                            
                            if (jsonData.type === 'progress') {
                                this.updateProgress(jsonData, startTime);
                            } else if (jsonData.type === 'final') {
                                finalResult = jsonData;
                            }
                        } catch (e) {
                            console.warn('解析JSON失败:', e);
                        }
                    }
                }
            }

            // 处理最终结果
            if (finalResult) {
                if (finalResult.status === 'success') {
                    this.showResult(finalResult);
                } else {
                    this.showError(finalResult.error || '生成失败');
                }
            } else {
                this.showError('未收到完整响应');
            }

        } finally {
            this.setGenerating(false);
        }
    }

    showProgress() {
        document.getElementById('generation-progress').style.display = 'block';
        document.getElementById('generation-progress').classList.add('fade-in');
    }

    updateProgress(data, startTime) {
        const elapsed = (Date.now() - startTime) / 1000;
        const chars = data.accumulated_length || 0;
        
        // 改进的进度条：基于生成活跃度而非字符数限制
        // 进度会持续增长，但永远不会到达100%（直到收到final消息）
        const timeBasedProgress = Math.min(elapsed * 1.2, 85); // 基于时间的进度，最多85%
        const activityProgress = Math.min(chars / 100, 10); // 基于活跃度的额外进度，最多10%
        const totalProgress = Math.min(timeBasedProgress + activityProgress, 95);
        
        document.getElementById('progress-fill').style.width = totalProgress + '%';
        document.getElementById('progress-chars').textContent = `${chars} 字符`;
        document.getElementById('progress-time').textContent = `${elapsed.toFixed(1)}s`;
        
        // 更新内容预览（只显示有意义的内容）
        if (data.chunk && this.isSignificantContent(data.chunk)) {
            const previewElement = document.getElementById('preview-text');
            
            // 检查用户是否在底部附近（容忍10px的误差）
            const isNearBottom = previewElement.scrollTop + previewElement.clientHeight >= previewElement.scrollHeight - 10;
            
            // 添加新内容
            previewElement.textContent += data.chunk;
            
            // 只有当用户在底部附近时才自动滚动
            if (isNearBottom) {
                previewElement.scrollTop = previewElement.scrollHeight;
            }
        }
    }

    isSignificantContent(chunk) {
        // 过滤掉不重要的内容，只显示有意义的部分
        if (!chunk || chunk.length < 2) return false;
        if (chunk.match(/^[<>{}[\]",:]+$/)) return false; // 只是JSON符号
        if (chunk.match(/^\s+$/)) return false; // 只是空白
        return true;
    }

    showResult(result) {
        // 先将进度条设为100%
        document.getElementById('progress-fill').style.width = '100%';
        
        // 短暂延迟后隐藏进度区域
        setTimeout(() => {
            document.getElementById('generation-progress').style.display = 'none';
        }, 1000);
        
        const resultSection = document.getElementById('result-section');
        resultSection.style.display = 'block';
        resultSection.classList.add('fade-in');
        
        // 填充基本信息
        document.getElementById('result-time').textContent = `耗时: ${result.processing_time?.toFixed(1) || 0}s`;
        document.getElementById('result-length').textContent = `长度: ${(result.raw_response || '').length || 0} 字符`;
        
        // 显示原始输出
        const rawOutputText = document.getElementById('raw-output-text');
        rawOutputText.textContent = result.raw_response || '无原始输出';
        
        // 重置JSON解析结果区域
        document.getElementById('json-parse-result').style.display = 'none';
        document.getElementById('parsed-plan').style.display = 'none';
        
        // 存储结果供后续操作使用
        this.currentResult = result;
        this.currentRawResponse = result.raw_response || '';
    }

    showQueueInfo() {
        const queueInfo = document.getElementById('queue-info');
        queueInfo.style.display = 'block';
        queueInfo.classList.add('fade-in');
        
        // 模拟队列信息
        document.getElementById('queue-position').textContent = Math.floor(Math.random() * 5) + 1;
        document.getElementById('estimated-time').textContent = `${Math.floor(Math.random() * 3) + 1}-${Math.floor(Math.random() * 5) + 3}分钟`;
    }

    showError(message) {
        document.getElementById('generation-progress').style.display = 'none';
        document.getElementById('queue-info').style.display = 'none';
        
        const errorSection = document.getElementById('error-section');
        errorSection.style.display = 'block';
        errorSection.classList.add('fade-in');
        
        document.getElementById('error-message').textContent = message;
    }

    resetUI() {
        document.getElementById('generation-progress').style.display = 'none';
        document.getElementById('result-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'none';
        document.getElementById('queue-info').style.display = 'none';
        
        // 重置进度
        document.getElementById('progress-fill').style.width = '0%';
        const previewElement = document.getElementById('preview-text');
        previewElement.textContent = '';
        previewElement.scrollTop = 0;  // 重置滚动位置到顶部
        document.getElementById('progress-chars').textContent = '0 字符';
        document.getElementById('progress-time').textContent = '0s';
    }

    setGenerating(isGenerating) {
        const button = document.getElementById('generate-btn');
        const btnText = button.querySelector('.btn-text');
        const btnLoader = button.querySelector('.btn-loader');
        
        button.disabled = isGenerating;
        btnText.style.display = isGenerating ? 'none' : 'inline';
        btnLoader.style.display = isGenerating ? 'inline' : 'none';
        
        if (isGenerating) {
            button.classList.add('pulse');
        } else {
            button.classList.remove('pulse');
        }
    }

    async loadHistory() {
        const historyList = document.getElementById('history-list');
        const loading = document.getElementById('history-loading');
        
        loading.style.display = 'block';
        
        try {
            const response = await fetch('/test-plan/records?page=1&page_size=20');
            const data = await response.json();
            
            loading.style.display = 'none';
            this.renderHistory(data.records);
            
        } catch (error) {
            console.error('加载历史记录失败:', error);
            loading.style.display = 'none';
            historyList.innerHTML = '<div class="error">加载历史记录失败</div>';
        }
    }

    async searchHistory() {
        const search = document.getElementById('history-search').value;
        const platform = document.getElementById('history-platform').value;
        const status = document.getElementById('history-status').value;
        
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (platform) params.append('platform', platform);
        if (status) params.append('status', status);
        
        try {
            const response = await fetch(`/test-plan/records?${params.toString()}`);
            const data = await response.json();
            this.renderHistory(data.records);
        } catch (error) {
            console.error('搜索失败:', error);
        }
    }

    renderHistory(records) {
        const historyList = document.getElementById('history-list');
        
        if (!records || records.length === 0) {
            historyList.innerHTML = '<div class="loading">暂无历史记录</div>';
            return;
        }
        
        const html = records.map(record => `
            <div class="history-item" onclick="app.showRecordDetail('${record.record_id}')">
                <div class="history-item-header">
                    <div class="history-item-title">${this.truncateText(record.original_instruction, 80)}</div>
                    <div class="history-item-meta">
                        <span class="status-badge status-${record.status}">${this.getStatusText(record.status)}</span>
                        <span>${record.detected_platform?.toUpperCase() || 'Unknown'}</span>
                        <span>${record.total_steps || 0}步</span>
                        <span>${new Date(record.created_at).toLocaleString()}</span>
                    </div>
                </div>
                <div class="history-item-preview">
                    ${record.plan_summary || '无摘要信息'}
                </div>
            </div>
        `).join('');
        
        historyList.innerHTML = html;
    }

    async showRecordDetail(recordId) {
        try {
            const response = await fetch(`/test-plan/records/${recordId}`);
            const record = await response.json();
            
            this.showJSONModal(record.generated_plan, record.original_instruction, record.raw_response);
        } catch (error) {
            console.error('获取记录详情失败:', error);
            alert('获取记录详情失败');
        }
    }

    showJSONModal(jsonData, title, rawResponse = null) {
        const modal = document.getElementById('json-modal');
        const viewer = document.getElementById('json-viewer');
        const rawButton = document.getElementById('view-raw-response');

        // 设置标题
        modal.querySelector('.modal-header h3').textContent = `📋 ${title || '测试计划详情'}`;

        // 存储当前JSON数据
        this.currentModalJSON = jsonData;
        this.currentRawResponse = rawResponse;

        // 重置视图状态
        this.currentViewMode = 'summary'; // summary 或 detailed
        this.stepPreviewEnabled = false;

        // 渲染简略版视图
        viewer.innerHTML = this.renderSummaryView(jsonData);

        // 显示或隐藏原始响应按钮
        if (rawResponse && rawResponse.trim()) {
            rawButton.style.display = 'inline-block';
        } else {
            rawButton.style.display = 'none';
        }

        // 显示模态框
        modal.classList.add('show');
    }

    closeModal() {
        document.getElementById('json-modal').classList.remove('show');
        // 重置状态
        this.currentViewMode = 'summary';
        this.stepPreviewEnabled = false;
    }

    renderSummaryView(jsonData) {
        const hasSteps = jsonData.steps && Array.isArray(jsonData.steps);

        let html = `
            <div class="json-viewer-header">
                <div class="view-mode-controls">
                    <button class="json-control-btn active" onclick="app.switchToSummaryView()">📋 概览</button>
                    <button class="json-control-btn" onclick="app.switchToDetailedView()">🔍 详细JSON</button>
                </div>
                <div class="json-controls">
                    ${hasSteps ? '<button class="json-control-btn" onclick="app.toggleStepPreview()">📝 步骤预览</button>' : ''}
                </div>
            </div>
            <div class="json-content-wrapper">
                ${this.renderPlanSummary(jsonData)}
                ${hasSteps ? this.renderStepsList(jsonData.steps) : ''}
            </div>
        `;

        return html;
    }

    renderPlanSummary(plan) {
        return `
            <div class="plan-summary-card">
                <h4>📋 测试计划概览</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">计划ID:</span>
                        <span class="summary-value">${plan.plan_id || 'N/A'}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">目标平台:</span>
                        <span class="summary-value">${(plan.platform || 'N/A').toUpperCase()}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">总步骤数:</span>
                        <span class="summary-value">${plan.total_steps || 0}</span>
                    </div>
                    <div class="summary-item full-width">
                        <span class="summary-label">计划摘要:</span>
                        <span class="summary-value">${plan.summary || plan.original_request || 'N/A'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    renderStepsList(steps) {
        if (!steps || !Array.isArray(steps)) return '';

        let html = `
            <div class="steps-list-card">
                <h4>🔄 测试步骤列表</h4>
                <div class="steps-container">
        `;

        steps.forEach((step, index) => {
            html += `
                <div class="step-item" onclick="app.toggleStepDetail(${index})">
                    <div class="step-header">
                        <div class="step-number">${step.step_id || index + 1}</div>
                        <div class="step-info">
                            <div class="step-action">${step.action || 'Unknown Action'}</div>
                            <div class="step-description">${step.description || 'No description'}</div>
                        </div>
                        <div class="step-toggle">▼</div>
                    </div>
                    <div class="step-details" id="step-detail-${index}" style="display: none;">
                        <div class="step-params">
                            <strong>参数:</strong>
                            <pre>${JSON.stringify(step.parameters || {}, null, 2)}</pre>
                        </div>
                        <div class="step-expected">
                            <strong>预期结果:</strong>
                            <span>${step.expected_result || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    switchToSummaryView() {
        if (this.currentViewMode === 'summary') return;

        this.currentViewMode = 'summary';
        this.stepPreviewEnabled = false;

        const viewer = document.getElementById('json-viewer');
        viewer.innerHTML = this.renderSummaryView(this.currentModalJSON);

        // 更新按钮状态
        this.updateViewModeButtons();
    }

    switchToDetailedView() {
        if (this.currentViewMode === 'detailed') return;

        this.currentViewMode = 'detailed';

        const viewer = document.getElementById('json-viewer');
        viewer.innerHTML = this.renderEnhancedJSONViewer(this.currentModalJSON);

        // 更新按钮状态
        this.updateViewModeButtons();

        // 初始化搜索功能
        this.initJSONSearch();
    }

    updateViewModeButtons() {
        const summaryBtn = document.querySelector('[onclick="app.switchToSummaryView()"]');
        const detailedBtn = document.querySelector('[onclick="app.switchToDetailedView()"]');

        if (summaryBtn && detailedBtn) {
            if (this.currentViewMode === 'summary') {
                summaryBtn.classList.add('active');
                detailedBtn.classList.remove('active');
            } else {
                summaryBtn.classList.remove('active');
                detailedBtn.classList.add('active');
            }
        }
    }

    toggleStepDetail(stepIndex) {
        const detailElement = document.getElementById(`step-detail-${stepIndex}`);
        const toggleElement = detailElement.parentElement.querySelector('.step-toggle');

        if (detailElement.style.display === 'none') {
            detailElement.style.display = 'block';
            toggleElement.textContent = '▲';
        } else {
            detailElement.style.display = 'none';
            toggleElement.textContent = '▼';
        }
    }

    renderEnhancedJSONViewer(jsonData) {
        let html = `
            <div class="json-viewer-header">
                <div class="view-mode-controls">
                    <button class="json-control-btn" onclick="app.switchToSummaryView()">📋 概览</button>
                    <button class="json-control-btn active" onclick="app.switchToDetailedView()">🔍 详细JSON</button>
                </div>
                <div class="json-search-container">
                    <input type="text" class="json-search-input" placeholder="搜索JSON内容..." id="json-search">
                    <span class="json-search-stats" id="json-search-stats"></span>
                </div>
                <div class="json-controls">
                    <button class="json-control-btn active" onclick="app.toggleJSONLineNumbers()">行号</button>
                    <button class="json-control-btn" onclick="app.expandAllJSON()">展开全部</button>
                    <button class="json-control-btn" onclick="app.collapseAllJSON()">折叠全部</button>
                    ${jsonData.steps ? `<button class="json-control-btn ${this.stepPreviewEnabled ? 'active' : ''}" onclick="app.toggleStepPreview()">📝 步骤预览</button>` : ''}
                </div>
            </div>
            <div class="json-content-wrapper">
                ${this.renderJSONWithLineNumbers(jsonData)}
            </div>
        `;

        return html;
    }

    renderJSONWithLineNumbers(obj, level = 0) {
        const jsonString = JSON.stringify(obj, null, 2);
        const lines = jsonString.split('\n');

        let html = '';
        lines.forEach((line, index) => {
            const lineNumber = index + 1;
            html += `
                <div class="json-line" data-line="${lineNumber}">
                    <div class="json-line-number">${lineNumber}</div>
                    <div class="json-line-content">${this.highlightJSONSyntax(line)}</div>
                </div>
            `;
        });

        return html;
    }

    highlightJSONSyntax(line) {
        // 转义HTML
        line = this.escapeHtml(line);

        // 高亮JSON语法
        line = line.replace(/"([^"]+)":/g, '<span class="json-key">"$1"</span>:');
        line = line.replace(/:\s*"([^"]*)"(?=,|\s*[}\]])/g, ': <span class="json-string">"$1"</span>');
        line = line.replace(/:\s*(\d+\.?\d*)(?=,|\s*[}\]])/g, ': <span class="json-number">$1</span>');
        line = line.replace(/:\s*(true|false)(?=,|\s*[}\]])/g, ': <span class="json-boolean">$1</span>');
        line = line.replace(/:\s*null(?=,|\s*[}\]])/g, ': <span class="json-null">null</span>');

        return line;
    }

    renderJSON(obj, level = 0) {
        if (obj === null) return '<span class="json-null">null</span>';
        if (typeof obj === 'string') return `<span class="json-string">"${this.escapeHtml(obj)}"</span>`;
        if (typeof obj === 'number') return `<span class="json-number">${obj}</span>`;
        if (typeof obj === 'boolean') return `<span class="json-boolean">${obj}</span>`;

        const indent = '  '.repeat(level);
        const nextIndent = '  '.repeat(level + 1);

        if (Array.isArray(obj)) {
            if (obj.length === 0) return '[]';

            const id = `array-${Math.random().toString(36).substr(2, 9)}`;
            let html = `<span class="json-collapsible" onclick="app.toggleJSON('${id}')">[</span>\n`;
            html += `<div class="json-content" id="${id}">`;

            obj.forEach((item, index) => {
                html += nextIndent + this.renderJSON(item, level + 1);
                if (index < obj.length - 1) html += ',';
                html += '\n';
            });

            html += `</div>${indent}]`;
            return html;
        }

        if (typeof obj === 'object') {
            const keys = Object.keys(obj);
            if (keys.length === 0) return '{}';

            const id = `object-${Math.random().toString(36).substr(2, 9)}`;
            let html = `<span class="json-collapsible" onclick="app.toggleJSON('${id}')">{</span>\n`;
            html += `<div class="json-content" id="${id}">`;

            keys.forEach((key, index) => {
                html += `${nextIndent}<span class="json-key">"${this.escapeHtml(key)}"</span>: `;
                html += this.renderJSON(obj[key], level + 1);
                if (index < keys.length - 1) html += ',';
                html += '\n';
            });

            html += `</div>${indent}}`;
            return html;
        }

        return String(obj);
    }

    initJSONSearch() {
        const searchInput = document.getElementById('json-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchJSON(e.target.value);
            });
        }
    }

    searchJSON(query) {
        const lines = document.querySelectorAll('.json-line');
        const stats = document.getElementById('json-search-stats');
        let matchCount = 0;

        lines.forEach(line => {
            const content = line.querySelector('.json-line-content');
            const originalText = content.textContent;

            if (query.trim() === '') {
                content.innerHTML = this.highlightJSONSyntax(originalText);
                line.style.display = '';
            } else {
                const regex = new RegExp(`(${this.escapeRegex(query)})`, 'gi');
                if (originalText.match(regex)) {
                    matchCount++;
                    const highlightedText = originalText.replace(regex, '<span class="json-highlight">$1</span>');
                    content.innerHTML = this.highlightJSONSyntax(highlightedText);
                    line.style.display = '';
                } else {
                    line.style.display = 'none';
                }
            }
        });

        if (stats) {
            stats.textContent = query.trim() ? `找到 ${matchCount} 处匹配` : '';
        }
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    toggleJSONLineNumbers() {
        const viewer = document.querySelector('.json-viewer');
        const lineNumbers = viewer.querySelectorAll('.json-line-number');
        const btn = event.target;

        if (btn.classList.contains('active')) {
            lineNumbers.forEach(ln => ln.style.display = 'none');
            btn.classList.remove('active');
        } else {
            lineNumbers.forEach(ln => ln.style.display = 'block');
            btn.classList.add('active');
        }
    }

    expandAllJSON() {
        const collapsedElements = document.querySelectorAll('.json-content.collapsed');
        const collapsedTriggers = document.querySelectorAll('.json-collapsible.collapsed');

        collapsedElements.forEach(el => el.classList.remove('collapsed'));
        collapsedTriggers.forEach(trigger => trigger.classList.remove('collapsed'));
    }

    collapseAllJSON() {
        const elements = document.querySelectorAll('.json-content');
        const triggers = document.querySelectorAll('.json-collapsible');

        elements.forEach(el => el.classList.add('collapsed'));
        triggers.forEach(trigger => trigger.classList.add('collapsed'));
    }

    toggleStepPreview() {
        const btn = event.target;
        const viewer = document.querySelector('.json-content-wrapper');

        if (this.stepPreviewEnabled) {
            // 移除步骤预览
            const previews = viewer.querySelectorAll('.json-step-preview');
            previews.forEach(preview => preview.remove());
            btn.classList.remove('active');
            this.stepPreviewEnabled = false;
        } else {
            // 添加步骤预览
            this.addStepPreviews();
            btn.classList.add('active');
            this.stepPreviewEnabled = true;
        }
    }

    addStepPreviews() {
        if (!this.currentModalJSON || !this.currentModalJSON.steps) return;

        // 先清除已有的预览，防止重复
        const existingPreviews = document.querySelectorAll('.json-step-preview');
        existingPreviews.forEach(preview => preview.remove());

        const steps = this.currentModalJSON.steps;
        const lines = document.querySelectorAll('.json-line');
        const processedSteps = new Set(); // 防止重复处理

        steps.forEach((step, index) => {
            const stepId = step.step_id;
            if (processedSteps.has(stepId)) return; // 跳过已处理的步骤

            // 查找包含step_id的行，使用更精确的匹配
            lines.forEach(line => {
                const content = line.querySelector('.json-line-content').textContent;
                // 使用正则表达式精确匹配step_id，避免部分匹配导致的重复
                const stepIdRegex = new RegExp(`"step_id":\\s*${stepId}(?![0-9])`);
                if (stepIdRegex.test(content) && !line.nextElementSibling?.classList.contains('json-step-preview')) {
                    const preview = document.createElement('div');
                    preview.className = 'json-step-preview';
                    preview.innerHTML = `
                        <div class="step-title">步骤 ${stepId}: ${step.action}</div>
                        <div class="step-desc">${step.description}</div>
                    `;
                    line.parentNode.insertBefore(preview, line.nextSibling);
                    processedSteps.add(stepId);
                }
            });
        });
    }

    toggleJSON(id) {
        const element = document.getElementById(id);
        const trigger = element.previousElementSibling;

        if (element.classList.contains('collapsed')) {
            element.classList.remove('collapsed');
            trigger.classList.remove('collapsed');
        } else {
            element.classList.add('collapsed');
            trigger.classList.add('collapsed');
        }
    }

    copyJSON() {
        const jsonText = JSON.stringify(this.currentModalJSON, null, 2);
        navigator.clipboard.writeText(jsonText).then(() => {
            this.showToast('JSON已复制到剪贴板');
        });
    }

    downloadJSON() {
        const jsonText = JSON.stringify(this.currentModalJSON, null, 2);
        const blob = new Blob([jsonText], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `test_plan_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('JSON文件已下载');
    }

    parseJSON() {
        if (!this.currentRawResponse) {
            this.showToast('没有可解析的内容');
            return;
        }

        const parseResultDiv = document.getElementById('json-parse-result');
        const parseStatusDiv = document.getElementById('parse-status');
        const parsedPlanDiv = document.getElementById('parsed-plan');
        const planSummaryDiv = document.getElementById('plan-summary');

        parseResultDiv.style.display = 'block';

        try {
            // 尝试从原始响应中提取JSON
            const parsedData = this.extractJSONFromResponse(this.currentRawResponse);
            
            if (parsedData) {
                // 解析成功
                parseStatusDiv.className = 'parse-status success';
                parseStatusDiv.textContent = '✅ JSON解析成功！测试计划已提取。';
                
                // 显示解析后的计划摘要
                parsedPlanDiv.style.display = 'block';
                planSummaryDiv.innerHTML = this.formatPlanSummary(parsedData);
                
                // 存储解析后的数据
                this.currentParsedPlan = parsedData;
            } else {
                // 解析失败
                parseStatusDiv.className = 'parse-status error';
                parseStatusDiv.textContent = '❌ JSON解析失败：无法从响应中提取有效的JSON格式测试计划。';
                parsedPlanDiv.style.display = 'none';
            }
        } catch (error) {
            // 解析异常
            parseStatusDiv.className = 'parse-status error';
            parseStatusDiv.textContent = `❌ JSON解析异常：${error.message}`;
            parsedPlanDiv.style.display = 'none';
        }
    }

    extractJSONFromResponse(responseText) {
        // 清理思维链标签
        let cleanedResponse = responseText;
        const thinkPattern = /<think>.*?<\/think>/g;
        cleanedResponse = cleanedResponse.replace(thinkPattern, '');
        
        // 策略1: 查找代码块中的JSON
        const codeBlockPattern = /```(?:json)?\s*(\{.*?\})\s*```/gs;
        let matches = cleanedResponse.match(codeBlockPattern);
        if (matches) {
            for (const match of matches) {
                try {
                    const jsonStr = match.replace(/```(?:json)?\s*/, '').replace(/\s*```/, '');
                    const data = JSON.parse(jsonStr);
                    if (this.validatePlanStructure(data)) {
                        return data;
                    }
                } catch (e) {
                    continue;
                }
            }
        }
        
        // 策略2: 查找完整的JSON对象
        const jsonPatterns = [
            /\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
            /\{.*?\}/g,
            /\{.*\}/g
        ];
        
        for (const pattern of jsonPatterns) {
            matches = cleanedResponse.match(pattern);
            if (matches) {
                for (const match of matches) {
                    try {
                        const data = JSON.parse(match);
                        if (this.validatePlanStructure(data)) {
                            return data;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }
        }
        
        return null;
    }

    validatePlanStructure(data) {
        const requiredFields = ['plan_id', 'steps', 'total_steps'];
        return requiredFields.every(field => data.hasOwnProperty(field)) &&
               Array.isArray(data.steps) &&
               data.steps.length > 0;
    }

    formatPlanSummary(plan) {
        return `
            <div><strong>计划ID:</strong> ${plan.plan_id || 'N/A'}</div>
            <div><strong>目标平台:</strong> ${(plan.platform || 'N/A').toUpperCase()}</div>
            <div><strong>总步骤数:</strong> ${plan.total_steps || 0}</div>
            <div><strong>计划摘要:</strong> ${plan.summary || plan.original_request || 'N/A'}</div>
            <div><strong>首个步骤:</strong> ${plan.steps?.[0]?.description || 'N/A'}</div>
            <div><strong>最后步骤:</strong> ${plan.steps?.[plan.steps.length - 1]?.description || 'N/A'}</div>
        `;
    }

    copyRawOutput() {
        if (this.currentRawResponse) {
            navigator.clipboard.writeText(this.currentRawResponse).then(() => {
                this.showToast('原始输出已复制到剪贴板');
            });
        }
    }

    copyParsedJSON() {
        if (this.currentParsedPlan) {
            const jsonText = JSON.stringify(this.currentParsedPlan, null, 2);
            navigator.clipboard.writeText(jsonText).then(() => {
                this.showToast('解析后的JSON已复制到剪贴板');
            });
        }
    }

    viewPlanDetails() {
        if (this.currentParsedPlan) {
            this.showJSONModal(this.currentParsedPlan, '解析后的测试计划', this.currentRawResponse);
        }
    }

    retryGeneration() {
        this.resetUI();
        this.generatePlan();
    }

    async loadPromptContent() {
        const promptContent = document.getElementById('prompt-content');
        
        try {
            // 显示加载状态
            promptContent.innerHTML = '<div class="loading"><span>📄 正在加载prompt说明...</span></div>';
            
            // 获取prompt内容
            const response = await fetch('/test-plan/prompt-info');
            const promptData = await response.json();
            
            // 渲染prompt内容
            this.renderPromptContent(promptData);
            
        } catch (error) {
            console.error('加载prompt内容失败:', error);
            promptContent.innerHTML = `
                <div class="warning-note">
                    <h4>❌ 加载失败</h4>
                    <p>无法加载prompt说明内容：${error.message}</p>
                    <button class="btn btn-secondary" onclick="app.loadPromptContent()">重试</button>
                </div>
            `;
        }
    }

    renderPromptContent(promptData) {
        const promptContent = document.getElementById('prompt-content');
        
        let html = `
            <div class="prompt-overview">
                <h4>📋 系统Prompt概览</h4>
                <p>本系统使用以下prompt模板将自然语言测试指令转换为结构化的JSON执行计划。</p>
            </div>
        `;

        // 渲染角色描述
        if (promptData.system_prompt_template?.role_description) {
            html += `
                <button class="prompt-collapsible">🤖 角色描述</button>
                <div class="prompt-collapsible-content">
                    <div class="example-block">
                        <p>${promptData.system_prompt_template.role_description}</p>
                    </div>
                </div>
            `;
        }

        // 渲染可用操作
        if (promptData.tool_definitions) {
            html += `
                <button class="prompt-collapsible">🛠️ 可用操作工具 (${Object.keys(promptData.tool_definitions).length}个)</button>
                <div class="prompt-collapsible-content">
            `;
            
            Object.entries(promptData.tool_definitions).forEach(([toolName, toolInfo]) => {
                html += `
                    <div class="tool-item" style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                        <h5 style="color: #0066cc; margin: 0 0 8px 0;">${toolName}</h5>
                        <p style="margin: 0 0 8px 0; color: #666;">${toolInfo.description}</p>
                        <div class="code-block" style="font-size: 0.8rem;">
                            必需参数: [${toolInfo.required_params.join(', ')}]<br>
                            可选参数: [${toolInfo.optional_params.join(', ')}]<br>
                            示例: ${JSON.stringify(toolInfo.example)}
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
        }

        // 渲染测试规则
        if (promptData.system_prompt_template?.test_rules) {
            html += `
                <button class="prompt-collapsible">📋 测试流程规则</button>
                <div class="prompt-collapsible-content">
            `;
            
            Object.entries(promptData.system_prompt_template.test_rules).forEach(([ruleKey, ruleInfo]) => {
                if (ruleKey !== 'header' && ruleInfo.title && ruleInfo.rules) {
                    html += `
                        <div class="rule-section" style="margin-bottom: 20px;">
                            <h5>${ruleInfo.title}</h5>
                            <ul>
                    `;
                    ruleInfo.rules.forEach(rule => {
                        html += `<li>${rule}</li>`;
                    });
                    html += '</ul></div>';
                }
            });
            
            html += '</div>';
        }

        // 渲染重要规则
        if (promptData.system_prompt_template?.important_rules?.rules) {
            html += `
                <button class="prompt-collapsible">⚠️ 重要规则</button>
                <div class="prompt-collapsible-content">
                    <div class="warning-note">
                        <ul>
            `;
            promptData.system_prompt_template.important_rules.rules.forEach(rule => {
                html += `<li>${rule}</li>`;
            });
            html += `
                        </ul>
                    </div>
                </div>
            `;
        }

        // 渲染输出格式
        if (promptData.system_prompt_template?.output_format) {
            html += `
                <button class="prompt-collapsible">📤 输出格式要求</button>
                <div class="prompt-collapsible-content">
                    <p>${promptData.system_prompt_template.output_format.description}</p>
                    <div class="code-block">
                        ${JSON.stringify(promptData.system_prompt_template.output_format.json_template, null, 2)}
                    </div>
                </div>
            `;
        }

        // 渲染示例
        if (promptData.system_prompt_template?.examples) {
            html += `
                <button class="prompt-collapsible">💡 转换示例</button>
                <div class="prompt-collapsible-content">
                    <div class="example-block">
                        <h5>输入示例：</h5>
                        <p>${promptData.system_prompt_template.examples.example_input}</p>
                    </div>
            `;
            
            if (promptData.system_prompt_template.examples.example_steps) {
                promptData.system_prompt_template.examples.example_steps.forEach(step => {
                    html += `
                        <div style="margin: 15px 0;">
                            <h5>${step.description}</h5>
                            <div class="code-block">
                                ${JSON.stringify(step.json_example, null, 2)}
                            </div>
                        </div>
                    `;
                });
            }
            
            html += '</div>';
        }

        // 渲染用户prompt模板
        if (promptData.user_prompt_template) {
            html += `
                <button class="prompt-collapsible">📝 用户Prompt模板</button>
                <div class="prompt-collapsible-content">
                    <div class="code-block">
                        ${promptData.user_prompt_template}
                    </div>
                </div>
            `;
        }

        promptContent.innerHTML = html;
        
        // 绑定折叠功能
        this.bindPromptCollapsibles();
        
        // 存储prompt数据供复制使用
        this.currentPromptData = promptData;
    }

    bindPromptCollapsibles() {
        const collapsibles = document.querySelectorAll('.prompt-collapsible');
        collapsibles.forEach(collapsible => {
            collapsible.addEventListener('click', function() {
                this.classList.toggle('active');
                const content = this.nextElementSibling;
                if (content.classList.contains('active')) {
                    content.classList.remove('active');
                } else {
                    content.classList.add('active');
                }
            });
        });
    }

    copyPromptContent() {
        if (this.currentPromptData) {
            const promptText = JSON.stringify(this.currentPromptData, null, 2);
            navigator.clipboard.writeText(promptText).then(() => {
                this.showToast('Prompt内容已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                this.showToast('复制失败，请手动选择内容复制');
            });
        }
    }

    showRawResponseModal() {
        if (this.currentRawResponse) {
            // 隐藏JSON模态框
            document.getElementById('json-modal').classList.remove('show');
            
            // 显示原始响应模态框
            const rawModal = document.getElementById('raw-response-modal');
            const rawViewer = document.getElementById('raw-response-viewer');
            
            // 设置内容
            rawViewer.textContent = this.currentRawResponse;
            
            // 根据内容类型设置样式
            rawViewer.className = 'raw-response-viewer';
            if (this.currentRawResponse.includes('ERROR') || this.currentRawResponse.includes('Failed')) {
                rawViewer.classList.add('error');
            } else if (this.currentRawResponse.includes('{') && this.currentRawResponse.includes('}')) {
                rawViewer.classList.add('success');
            }
            
            // 显示模态框
            rawModal.classList.add('show');
        }
    }

    closeRawResponseModal() {
        document.getElementById('raw-response-modal').classList.remove('show');
    }

    copyRawResponse() {
        if (this.currentRawResponse) {
            navigator.clipboard.writeText(this.currentRawResponse).then(() => {
                this.showToast('原始响应已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                this.showToast('复制失败，请手动选择内容复制');
            });
        }
    }

    backToJsonView() {
        // 隐藏原始响应模态框
        this.closeRawResponseModal();
        
        // 显示JSON模态框
        document.getElementById('json-modal').classList.add('show');
    }

    showToast(message) {
        // 简单的提示实现
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    getStatusText(status) {
        const statusMap = {
            'success': '成功',
            'failed': '失败',
            'error': '错误'
        };
        return statusMap[status] || status;
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
const app = new TestPlanGenerator();