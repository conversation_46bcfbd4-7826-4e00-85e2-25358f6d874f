/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.model-info {
    margin-top: 15px;
}

.model-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.model-badge:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* 标签页导航 */
.nav-tabs {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: #f8f9fa;
    color: #666;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background: #e9ecef;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    border-bottom: 3px solid #667eea;
}

/* 标签页内容 */
.tab-content {
    display: none;
    background: white;
    border-radius: 0 0 8px 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tab-content.active {
    display: block;
}

/* 表单样式 */
.input-section {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

input[type="text"],
textarea,
select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea {
    resize: vertical;
    min-height: 100px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 系统状态 */
.system-status {
    margin: 20px 0;
    padding: 15px;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-icon {
    font-size: 1.2rem;
}

/* 排队信息 */
.queue-info {
    margin: 20px 0;
}

.queue-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.queue-card h3 {
    color: #856404;
    margin-bottom: 10px;
}

.queue-details {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 15px;
}

.queue-details span {
    color: #856404;
}

/* 生成进度 - 放大作为主体 */
.generation-progress {
    margin: 20px 0;
    min-height: 500px; /* 确保足够的高度 */
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.progress-stats {
    display: flex;
    gap: 20px;
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 12px; /* 加粗进度条 */
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.content-preview {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 25px;
    min-height: 400px; /* 大幅增加高度 */
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.content-preview h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.preview-text {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Fira Mono', 'Roboto Mono', 'Courier New', monospace;
    font-size: 1rem; /* 增大字体 */
    color: #333;
    min-height: 350px; /* 大幅增加高度 */
    max-height: 500px; /* 最大高度限制 */
    overflow-y: auto;
    line-height: 1.6;
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #ddd;
    white-space: pre-wrap; /* 保持格式 */
    word-wrap: break-word;
}

/* 结果区域 */
.result-section {
    margin: 20px 0;
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 20px;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.result-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: #0c5460;
}

.raw-output-preview {
    margin: 20px 0;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.raw-output-preview h4 {
    margin-bottom: 12px;
    color: #495057;
    font-size: 1.1rem;
}

.raw-output-text {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Roboto Mono', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #333;
}

.result-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.json-parse-result {
    border-top: 2px solid #bee5eb;
    padding-top: 20px;
    margin-top: 20px;
}

.parse-status {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-weight: 500;
}

.parse-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.parse-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.parsed-plan {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.parsed-plan h4 {
    margin-bottom: 10px;
    color: #0056b3;
}

.plan-summary {
    background: white;
    border: 1px solid #cce7ff;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
    font-size: 0.95rem;
    line-height: 1.4;
}

.plan-actions {
    display: flex;
    gap: 10px;
}

/* 错误区域 */
.error-section {
    margin: 20px 0;
}

.error-card {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.error-card h3 {
    color: #721c24;
    margin-bottom: 10px;
}

.error-card p {
    color: #721c24;
    margin-bottom: 15px;
}

/* 历史记录 */
.history-controls {
    margin-bottom: 30px;
}

.search-section {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.history-list {
    min-height: 400px;
}

.loading {
    text-align: center;
    padding: 50px;
    color: #666;
    font-size: 1.1rem;
}

.history-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.history-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.history-item-title {
    font-weight: 600;
    color: #333;
    flex: 1;
    margin-right: 15px;
}

.history-item-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: #666;
}

.history-item-preview {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.4;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-error {
    background: #fff3cd;
    color: #856404;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 95%;
    max-width: 1000px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.25rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* 原始响应查看器 */
.raw-response-viewer {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Roboto Mono', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    max-height: 500px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #333;
}

.raw-response-viewer.error {
    background: #fff5f5;
    border-color: #feb2b2;
    color: #c53030;
}

.raw-response-viewer.success {
    background: #f0fff4;
    border-color: #9ae6b4;
    color: #276749;
}

/* JSON查看器 */
.json-viewer {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    max-height: 60vh;
    overflow: auto;
    position: relative;
}

.json-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.json-search-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.json-search-input {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.8rem;
    width: 200px;
}

.json-search-stats {
    font-size: 0.75rem;
    color: #666;
}

.json-controls {
    display: flex;
    gap: 8px;
}

.json-control-btn {
    padding: 4px 8px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
}

.json-control-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.json-control-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.json-content-wrapper {
    position: relative;
}

.json-line {
    display: flex;
    align-items: flex-start;
    min-height: 1.5em;
}

.json-line-number {
    color: #999;
    font-size: 0.75rem;
    width: 40px;
    text-align: right;
    padding-right: 12px;
    user-select: none;
    flex-shrink: 0;
    border-right: 1px solid #e9ecef;
    margin-right: 12px;
}

.json-line-content {
    flex: 1;
    white-space: pre-wrap;
    word-break: break-all;
}

.json-key {
    color: #0066cc;
    font-weight: 600;
}

.json-string {
    color: #d73a49;
}

.json-number {
    color: #005cc5;
}

.json-boolean {
    color: #e36209;
    font-weight: 600;
}

.json-null {
    color: #6f42c1;
    font-style: italic;
}

.json-collapsible {
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.json-collapsible:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.json-collapsible:before {
    content: '▼';
    color: #666;
    font-size: 0.8em;
    margin-right: 4px;
    transition: transform 0.2s;
}

.json-collapsible.collapsed:before {
    content: '▶';
    transform: rotate(0deg);
}

.json-content {
    margin-left: 20px;
}

.json-content.collapsed {
    display: none;
}

.json-highlight {
    background-color: #fff3cd;
    padding: 1px 2px;
    border-radius: 2px;
}

.json-step-preview {
    background: #e8f4fd;
    border-left: 3px solid #0066cc;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 0 4px 4px 0;
    font-size: 0.8rem;
}

.json-step-preview .step-title {
    font-weight: 600;
    color: #0066cc;
    margin-bottom: 4px;
}

.json-step-preview .step-desc {
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .search-section {
        grid-template-columns: 1fr;
    }
    
    .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .history-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* Prompt说明页面 */
.prompt-section {
    max-width: 100%;
}

.prompt-header {
    text-align: center;
    margin-bottom: 30px;
}

.prompt-header h3 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.prompt-subtitle {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
}

.prompt-content {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 25px;
    min-height: 500px;
    max-height: 700px;
    overflow-y: auto;
    margin-bottom: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

.prompt-content h4 {
    color: #495057;
    font-size: 1.3rem;
    margin: 25px 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #667eea;
}

.prompt-content h5 {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 20px 0 10px 0;
    font-weight: 600;
}

.prompt-content ul {
    margin: 10px 0 15px 20px;
    padding-left: 0;
}

.prompt-content li {
    margin: 8px 0;
    color: #495057;
    list-style-type: disc;
}

.prompt-content .code-block {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 6px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-size: 0.9rem;
    overflow-x: auto;
    margin: 15px 0;
    border-left: 4px solid #667eea;
}

.prompt-content .example-block {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #0066cc;
}

.prompt-content .important-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #ffc107;
}

.prompt-content .warning-note {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #dc3545;
}

.prompt-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.prompt-collapsible {
    background: #667eea;
    color: white;
    cursor: pointer;
    padding: 12px 15px;
    border: none;
    border-radius: 6px;
    text-align: left;
    outline: none;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
    margin: 10px 0;
    width: 100%;
}

.prompt-collapsible:hover {
    background: #5a67d8;
}

.prompt-collapsible.active {
    background: #4c51bf;
}

.prompt-collapsible:after {
    content: '\002B'; /* + */
    color: white;
    font-weight: bold;
    float: right;
    margin-left: 5px;
}

.prompt-collapsible.active:after {
    content: "\2212"; /* - */
}

.prompt-collapsible-content {
    padding: 0 15px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-out;
    background: white;
    border-left: 3px solid #667eea;
    margin-bottom: 10px;
}

.prompt-collapsible-content.active {
    max-height: 2000px;
    padding: 15px;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.pulse {
    animation: pulse 2s infinite;
}